/**
 * Pinia 全局混入
 * 类似 uv-ui 的 $uv.mixin.js，但针对 Pinia 优化
 */

import { 
  setStoreValue, 
  getStoreValue, 
  callStoreAction, 
  restoreStoreFromStorage,
  clearStoreStorage 
} from '@/utils/pinia-helper'

export default {
  created() {
    // 将 Pinia 简化方法挂载到全局
    if (!uni.$pinia) {
      uni.$pinia = {}
    }
    
    // 设置值的方法 (类似 uv-ui 的 vuex 方法)
    uni.$pinia.set = (path, value, persist = true) => {
      setStoreValue(path, value, persist)
    }
    
    // 获取值的方法
    uni.$pinia.get = (path) => {
      return getStoreValue(path)
    }
    
    // 调用 action 的方法
    uni.$pinia.action = (path, params) => {
      return callStoreAction(path, params)
    }
    
    // 恢复数据的方法
    uni.$pinia.restore = (storeName) => {
      restoreStoreFromStorage(storeName)
    }
    
    // 清除存储的方法
    uni.$pinia.clear = (storeName) => {
      clearStoreStorage(storeName)
    }
    
    // 批量设置的方法
    uni.$pinia.batch = (updates) => {
      Object.keys(updates).forEach(path => {
        setStoreValue(path, updates[path])
      })
    }
    
    // 也可以挂载到 this 上，方便在组件中使用
    this.$pinia = uni.$pinia
  }
}
