<template>
  <view class="content">
    <image class="logo" src="/static/logo.png"></image>

    <!-- 用户信息区域 -->
    <view class="user-section">
      <view class="user-info">
        <view class="avatar">{{ userStore.userInitials }}</view>
        <text class="user-name">{{ userStore.displayName }}</text>
        <text class="user-status">{{ userStore.isLoggedIn ? '已登录' : '未登录' }}</text>
      </view>

      <view class="user-actions">
        <button v-if="!userStore.isLoggedIn" class="btn btn-primary" @click="login">登录</button>
        <button v-else class="btn btn-danger" @click="logout">登出</button>
        <button class="btn btn-info" @click="toggleTheme">
          {{ userStore.preferences.theme === 'light' ? '🌙' : '☀️' }} 切换主题
        </button>
      </view>
    </view>

    <!-- Pinia 计数器演示 -->
    <view class="counter-section">
      <view class="section-title">
        <text class="title">{{ counterStore.name }}</text>
      </view>

      <view class="counter-display">
        <text class="counter-text">当前计数: {{ counterStore.count }}</text>
        <text class="counter-text">双倍计数: {{ counterStore.doubleCount }}</text>
        <text class="counter-text">计数+1: {{ counterStore.countPlusOne }}</text>
      </view>

      <view class="button-group">
        <button class="btn btn-primary" @click="increment">+1</button>
        <button class="btn btn-danger" @click="decrement">-1</button>
        <button class="btn btn-warning" @click="reset">重置</button>
        <button class="btn btn-success" @click="setToTen">设为10</button>
      </view>
    </view>
  </view>
</template>

<script>
import { useCounterStore, useUserStore } from '@/stores'

export default {
  data() {
    return {
      title: 'Pinia 状态管理演示'
    }
  },

  setup() {
    const counterStore = useCounterStore()
    const userStore = useUserStore()

    return {
      counterStore,
      userStore
    }
  },

  onLoad() {
    console.log('页面加载，Pinia stores 已初始化')
    console.log('计数器状态:', this.counterStore.count)
    console.log('用户状态:', this.userStore.displayName)
  },

  methods: {
    // 计数器方法
    increment() {
      this.counterStore.increment()
    },

    decrement() {
      this.counterStore.decrement()
    },

    reset() {
      this.counterStore.reset()
    },

    setToTen() {
      this.counterStore.setCount(10)
    },

    // 用户方法
    login() {
      const userInfo = {
        name: 'Vue开发者',
        email: '<EMAIL>',
        avatar: ''
      }
      this.userStore.login(userInfo)
    },

    logout() {
      this.userStore.logout()
    },

    toggleTheme() {
      this.userStore.toggleTheme()
      console.log('主题已切换为:', this.userStore.preferences.theme)
    }
  },
}
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  min-height: 100vh;
}

.logo {
  height: 160rpx;
  width: 160rpx;
  margin-bottom: 40rpx;
}

/* 用户信息区域 */
.user-section {
  width: 100%;
  max-width: 600rpx;
  margin-bottom: 60rpx;
}

.user-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  color: white;
  margin-bottom: 30rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20rpx;
  font-size: 36rpx;
  font-weight: bold;
}

.user-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.user-status {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

.user-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.section-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  color: #2c3e50;
  font-weight: bold;
}

/* Pinia 计数器样式 */
.counter-section {
  width: 100%;
  max-width: 600rpx;
}

.counter-display {
  background: #f8f9fa;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.counter-text {
  display: block;
  font-size: 32rpx;
  color: #495057;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20rpx;
}

.btn {
  flex: 1;
  min-width: 140rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: white;
  border: none;
  font-weight: bold;
}

.btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
}

.btn-danger {
  background: linear-gradient(45deg, #dc3545, #c82333);
}

.btn-warning {
  background: linear-gradient(45deg, #ffc107, #e0a800);
  color: #212529;
}

.btn-success {
  background: linear-gradient(45deg, #28a745, #1e7e34);
}

.btn-info {
  background: linear-gradient(45deg, #17a2b8, #138496);
  min-width: 200rpx;
}

.btn:active {
  transform: scale(0.95);
  transition: transform 0.1s;
}

.btn:hover {
  opacity: 0.9;
}
</style>
