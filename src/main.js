import {
	createSSRApp
} from "vue";
import { createPinia } from 'pinia'
import piniaMixin from '@/mixins/pinia-mixin'
import { initStores } from '@/stores'

import App from "./App.vue";
export function createApp() {
	const app = createSSRApp(App);

	// 使用 Pinia
	app.use(createPinia())

	// 全局混入 Pinia 助手方法
	app.mixin(piniaMixin)

	// 初始化 stores
	initStores()

	return {
		app,
		Pinia: createPinia
	};
}
