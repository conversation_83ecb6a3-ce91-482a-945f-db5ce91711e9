// 导出所有 stores
export { useCounterStore } from './counter'
export { useUserStore } from './user'

// 导入 Pinia 助手
import { registerStore } from '@/utils/pinia-helper'
import { useCounterStore } from './counter'
import { useUserStore } from './user'

// 注册所有 stores 到全局映射
export const initStores = () => {
  console.log('Pinia stores 开始初始化...')

  // 注册 stores
  registerStore('counter', useCounterStore)
  registerStore('user', useUserStore)

  console.log('Pinia stores 已初始化完成')
  console.log('现在可以使用 uni.$pinia.set() 和 uni.$pinia.get() 方法了')
}
