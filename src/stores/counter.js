import { defineStore } from 'pinia'

export const useCounterStore = defineStore('counter', {
  state: () => ({
    count: 0,
    name: 'Pinia Counter'
  }),
  
  getters: {
    doubleCount: (state) => state.count * 2,
    countPlusOne() {
      return this.count + 1
    }
  },
  
  actions: {
    increment() {
      this.count++
    },
    
    decrement() {
      this.count--
    },
    
    reset() {
      this.count = 0
    },
    
    setCount(value) {
      this.count = value
    }
  }
})
