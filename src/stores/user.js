import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: {
      name: '游客',
      avatar: '',
      email: '',
      isLogin: false
    },
    preferences: {
      theme: 'light',
      language: 'zh-CN'
    }
  }),
  
  getters: {
    displayName: (state) => {
      return state.userInfo.isLogin ? state.userInfo.name : '未登录用户'
    },
    
    isLoggedIn: (state) => state.userInfo.isLogin,
    
    userInitials: (state) => {
      if (!state.userInfo.name) return '?'
      return state.userInfo.name.charAt(0).toUpperCase()
    }
  },
  
  actions: {
    // 登录
    login(userInfo) {
      this.userInfo = {
        ...userInfo,
        isLogin: true
      }
      console.log('用户登录成功:', this.userInfo.name)
    },
    
    // 登出
    logout() {
      this.userInfo = {
        name: '游客',
        avatar: '',
        email: '',
        isLogin: false
      }
      console.log('用户已登出')
    },
    
    // 更新用户信息
    updateUserInfo(info) {
      this.userInfo = {
        ...this.userInfo,
        ...info
      }
    },
    
    // 更新偏好设置
    updatePreferences(prefs) {
      this.preferences = {
        ...this.preferences,
        ...prefs
      }
    },
    
    // 切换主题
    toggleTheme() {
      this.preferences.theme = this.preferences.theme === 'light' ? 'dark' : 'light'
    }
  }
})
