/**
 * Pinia 简化调用工具
 * 类似 uv-ui 的 vuex 封装，但更强大
 */

// 存储所有 store 实例的映射
const storeMap = new Map()

/**
 * 注册 store 到全局映射
 * @param {string} name store 名称
 * @param {Function} useStore store 函数
 */
export function registerStore(name, useStore) {
  storeMap.set(name, useStore)
}

/**
 * 获取 store 实例
 * @param {string} name store 名称
 * @returns {Object} store 实例
 */
export function getStore(name) {
  const useStore = storeMap.get(name)
  if (!useStore) {
    console.warn(`Store "${name}" not found. Please register it first.`)
    return null
  }
  return useStore()
}

/**
 * 设置 store 中的值 (类似 uv-ui 的 vuex 方法)
 * @param {string} path 路径，如 'counter.count' 或 'user.info.name'
 * @param {any} value 要设置的值
 * @param {boolean} persist 是否持久化到本地存储
 */
export function setStoreValue(path, value, persist = true) {
  const pathArray = path.split('.')
  const storeName = pathArray[0]
  const store = getStore(storeName)
  
  if (!store) return
  
  if (pathArray.length === 2) {
    // 简单路径：counter.count
    const property = pathArray[1]
    store[property] = value
  } else if (pathArray.length > 2) {
    // 复杂路径：user.info.name
    let obj = store
    for (let i = 1; i < pathArray.length - 1; i++) {
      obj = obj[pathArray[i]]
    }
    obj[pathArray[pathArray.length - 1]] = value
  }
  
  // 持久化到本地存储
  if (persist) {
    try {
      uni.setStorageSync(`pinia_${storeName}`, store.$state)
    } catch (e) {
      console.warn('Failed to persist store data:', e)
    }
  }
}

/**
 * 获取 store 中的值
 * @param {string} path 路径，如 'counter.count' 或 'user.info.name'
 * @returns {any} 值
 */
export function getStoreValue(path) {
  const pathArray = path.split('.')
  const storeName = pathArray[0]
  const store = getStore(storeName)
  
  if (!store) return undefined
  
  let result = store
  for (let i = 1; i < pathArray.length; i++) {
    result = result[pathArray[i]]
  }
  return result
}

/**
 * 调用 store 中的 action
 * @param {string} path 路径，如 'counter.increment' 或 'user.login'
 * @param {any} params 参数
 */
export function callStoreAction(path, params) {
  const pathArray = path.split('.')
  const storeName = pathArray[0]
  const actionName = pathArray[1]
  const store = getStore(storeName)
  
  if (!store || typeof store[actionName] !== 'function') {
    console.warn(`Action "${actionName}" not found in store "${storeName}"`)
    return
  }
  
  return store[actionName](params)
}

/**
 * 从本地存储恢复 store 数据
 * @param {string} storeName store 名称
 */
export function restoreStoreFromStorage(storeName) {
  try {
    const stored = uni.getStorageSync(`pinia_${storeName}`)
    if (stored) {
      const store = getStore(storeName)
      if (store && store.$patch) {
        store.$patch(stored)
      }
    }
  } catch (e) {
    console.warn('Failed to restore store data:', e)
  }
}

/**
 * 清除 store 的本地存储
 * @param {string} storeName store 名称
 */
export function clearStoreStorage(storeName) {
  try {
    uni.removeStorageSync(`pinia_${storeName}`)
  } catch (e) {
    console.warn('Failed to clear store storage:', e)
  }
}
