# 🚀 Pinia 简化调用封装使用指南

## 📖 概述

这个封装提供了类似 uv-ui Vuex 封装的简化调用方式，让 Pinia 的使用更加便捷和直观。

## ✨ 特性

- 🎯 **简化调用**：类似 `uni.$uv.vuex()` 的调用方式
- 💾 **自动持久化**：支持数据自动保存到本地存储
- 🔄 **批量操作**：支持批量设置多个状态
- 🎪 **全局访问**：在任何地方都可以使用
- 🛠️ **类型安全**：保持 Pinia 的类型推导能力

## 🎯 基本用法

### 1. 设置状态值

```javascript
// 设置简单值
uni.$pinia.set('counter.count', 100)

// 设置嵌套对象值
uni.$pinia.set('user.userInfo.name', '新用户名')

// 设置值但不持久化
uni.$pinia.set('counter.count', 50, false)
```

### 2. 获取状态值

```javascript
// 获取简单值
const count = uni.$pinia.get('counter.count')

// 获取嵌套对象值
const userName = uni.$pinia.get('user.userInfo.name')
```

### 3. 调用 Actions

```javascript
// 调用简单 action
uni.$pinia.action('counter.increment')

// 调用带参数的 action
uni.$pinia.action('user.login', {
  name: '用户名',
  email: '<EMAIL>'
})
```

### 4. 批量操作

```javascript
// 批量设置多个值
uni.$pinia.batch({
  'counter.count': 100,
  'user.userInfo.name': '批量用户',
  'user.preferences.theme': 'dark'
})
```

### 5. 数据持久化

```javascript
// 恢复指定 store 的数据
uni.$pinia.restore('counter')
uni.$pinia.restore('user')

// 清除指定 store 的本地存储
uni.$pinia.clear('counter')
```

## 🔧 在组件中使用

### 方式一：全局调用

```javascript
export default {
  methods: {
    handleClick() {
      // 直接使用全局方法
      uni.$pinia.set('counter.count', 100)
      
      const count = uni.$pinia.get('counter.count')
      console.log('当前计数:', count)
    }
  }
}
```

### 方式二：组件内调用

```javascript
export default {
  methods: {
    handleClick() {
      // 使用 this.$pinia (通过 mixin 注入)
      this.$pinia.set('counter.count', 100)
      
      const count = this.$pinia.get('counter.count')
      console.log('当前计数:', count)
    }
  }
}
```

## 📝 与传统方式对比

### 传统 Pinia 方式

```javascript
import { useCounterStore, useUserStore } from '@/stores'

export default {
  setup() {
    const counterStore = useCounterStore()
    const userStore = useUserStore()
    
    return { counterStore, userStore }
  },
  
  methods: {
    handleUpdate() {
      this.counterStore.count = 100
      this.userStore.userInfo.name = '新用户名'
      this.counterStore.increment()
    }
  }
}
```

### 简化封装方式

```javascript
export default {
  methods: {
    handleUpdate() {
      uni.$pinia.set('counter.count', 100)
      uni.$pinia.set('user.userInfo.name', '新用户名')
      uni.$pinia.action('counter.increment')
    }
  }
}
```

## 🎪 高级用法

### 条件设置

```javascript
// 根据条件设置不同的值
const theme = uni.$pinia.get('user.preferences.theme')
if (theme === 'light') {
  uni.$pinia.set('user.preferences.theme', 'dark')
} else {
  uni.$pinia.set('user.preferences.theme', 'light')
}
```

### 链式调用

```javascript
// 虽然不支持真正的链式调用，但可以连续调用
uni.$pinia.set('counter.count', 0)
uni.$pinia.action('counter.increment')
uni.$pinia.action('counter.increment')
```

## 🚨 注意事项

1. **Store 注册**：确保在 `stores/index.js` 中注册了所有 store
2. **路径格式**：使用点号分隔的路径格式，如 `'store.property'`
3. **类型安全**：虽然简化了调用，但失去了一些类型检查
4. **性能考虑**：频繁调用时建议使用传统方式

## 🔄 迁移指南

如果您之前使用 uv-ui 的 Vuex 封装，迁移非常简单：

```javascript
// uv-ui Vuex 方式
uni.$uv.vuex('user.name', '史诗')

// 新的 Pinia 方式
uni.$pinia.set('user.userInfo.name', '史诗')
```

主要区别：
- `uni.$uv.vuex()` → `uni.$pinia.set()`
- 支持更多操作：`get()`, `action()`, `batch()` 等
- 更好的错误处理和调试信息
